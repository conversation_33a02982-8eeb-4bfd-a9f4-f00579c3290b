"use client";

import type React from "react";
import { useEffect, useState, useMemo } from "react";
import { useSelector, useDispatch } from "react-redux";
import { usePaymentDetails } from "@/context/PaymentDetailsContext";
import {
  Listbox,
  ListboxButton,
  ListboxOption,
  ListboxOptions,
} from "@headlessui/react";
import { ChevronDown } from "lucide-react";
import { debounce } from "lodash";
import type { AppState } from "@/store/store";
import { updateTripSummary } from "@/store/slices/tripSummary";
import { agentPostMethod } from "@/utils/api";
import PhoneNumberInput from "../phoneNumber";
import { useFlightContext } from "@/context/FlightContext";
import { useCustomSession } from "@/hooks/use-custom-session";

interface PaymentFormProps {
  onProceed: () => void;
  onLoaded: () => void;
  paymenFormErrors: any;
}

type Country = {
  code: string;
  name: string;
};

type CountryState = {
  search: string;
  loading: boolean;
  countryList: Country[];
};

const PaymentForm: React.FC<PaymentFormProps> = ({
  onProceed,
  onLoaded,
  paymenFormErrors,
}) => {
  const { paymentFormData, setPaymentFormData } = usePaymentDetails();
  const { updateGlobalPopup } = useFlightContext();
  const [expMonth, setExpMonth] = useState("");
  const [expYear, setExpYear] = useState("");
  const [country, setCountry] = useState<CountryState>({
    search: "",
    loading: false,
    countryList: [],
  });
  const [enablePay, setEnablePay] = useState(false);

  // Track which fields have been interacted with
  const [touchedFields, setTouchedFields] = useState<Set<string>>(new Set());
  // Track real-time validation errors
  const [realTimeErrors, setRealTimeErrors] = useState<{
    [key: string]: string;
  }>({});

  const { data: session, status } = useCustomSession();
  const token = session?.accessToken;

  const tripSummaryDetails = useSelector(
    (state: AppState) => state.tripSummary
  );
  const { currentUserDetails } = useSelector(
    (state: AppState) => state.userDetails
  );
  const dispatch = useDispatch();

  // Validation function that returns error message or empty string if valid
  const validateField = (field: string, value?: string): string => {
    const paymentDetails = tripSummaryDetails?.paymentDetails;

    switch (field) {
      case "title":
        const titleValue = value || paymentDetails?.title;
        if (!titleValue || titleValue === "") {
          return "Title is required";
        }
        return "";

      case "name":
        const nameValue = value || paymentDetails?.name;
        if (!nameValue || nameValue.trim() === "") {
          return "Name is required";
        }
        if (nameValue.trim().length < 2) {
          return "Name must be at least 2 characters";
        }
        return "";

      case "email":
        const emailValue = value || paymentDetails?.email;
        if (!emailValue || emailValue.trim() === "") {
          return "Email is required";
        }
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(emailValue.trim())) {
          return "Please enter a valid email address";
        }
        return "";

      case "street":
        const streetValue = value || paymentDetails?.street;
        if (!streetValue || streetValue.trim() === "") {
          return " Street Value is required";
        }
        return "";
      case "city":
        const cityValue = value || paymentDetails?.city;
        if (!cityValue || cityValue.trim() === "") {
          return "City is required";
        }
        return "";

      case "state":
        const stateValue = value || paymentDetails?.state;
        if (!stateValue || stateValue.trim() === "") {
          return "State is required";
        }
        return "";

      case "postcode":
        const postcodeValue = value || paymentDetails?.postcode;
        if (!postcodeValue || postcodeValue.trim() === "") {
          return "Zip code is required";
        }
        if (postcodeValue.trim().length < 3) {
          return "Please enter a valid zip code";
        }
        return "";

      case "country_code":
        if (
          !paymentDetails?.country_code ||
          paymentDetails.country_code === ""
        ) {
          return "Country is required";
        }
        return "";

      case "phone_int_code":
        if (
          !paymentDetails?.phone_int_code ||
          !paymentDetails?.contact_details_phone_number
        ) {
          return "Phone number is required";
        }
        if (paymentDetails.contact_details_phone_number.length < 7) {
          return "Please enter a valid phone number";
        }
        return "";

      default:
        return "";
    }
  };

  // Function to mark a field as touched and validate it
  const markFieldAsTouched = (field: string, value?: string) => {
    setTouchedFields((prev) => new Set([...prev, field]));

    // Validate the field and update real-time errors
    const error = validateField(field, value);
    setRealTimeErrors((prev) => {
      const newErrors = { ...prev };
      if (error) {
        newErrors[field] = error;
      } else {
        delete newErrors[field];
      }
      return newErrors;
    });
  };

  // Function to get the error message to display
  const getFieldError = (field: string) => {
    // If field has been touched, show real-time validation error
    if (touchedFields.has(field)) {
      return realTimeErrors[field] || null;
    }

    // If field hasn't been touched, show submission error
    if (paymenFormErrors[field]) {
      return paymenFormErrors[field];
    }

    return null;
  };

  useEffect(() => {
    if (expMonth && expYear) {
      setPaymentFormData({
        ...paymentFormData,
        cc_expiry_date: `${expMonth}/${expYear}`,
      });
    }
    onLoaded();
  }, [expMonth, expYear, paymentFormData, setPaymentFormData, onLoaded]);

  // Initialize country search from Redux state
  useEffect(() => {
    if (tripSummaryDetails?.paymentDetails?.country_name && !country.search) {
      setCountry((prev) => ({
        ...prev,
        search: tripSummaryDetails.paymentDetails.country_name,
      }));
    }
  }, [tripSummaryDetails?.paymentDetails?.country_name, country.search]);

  const handleChange =
    (field: keyof typeof paymentFormData) =>
    (e: React.ChangeEvent<HTMLInputElement>) => {
      let value = e.target.value;

      if (field === "cc_number") {
        if (value.length > 16) {
          return;
        }
        value = value.replace(/\D/g, ""); // Remove non-digits
      }
      if (field === "card_exp_year") {
        if (value.length > 2) {
          return;
        }
        value = value.replace(/\D/g, ""); // Remove non-digits
      }
      if (field === "card_exp_month") {
        if (value.length > 2) {
          return;
        }
        value = value.replace(/\D/g, ""); // Remove non-digits
      }
      if (field === "cc_security_code") {
        if (value.length > 3) {
          return;
        }
        value = value.replace(/\D/g, ""); // Remove non-digits
      }

      dispatch(
        updateTripSummary({
          paymentDetails: {
            ...tripSummaryDetails.paymentDetails,
            [field]: value,
          },
        })
      );

      // Mark field as touched and validate
      markFieldAsTouched(field, value);
    };

  const handlePhoneNumber = (
    phone: string,
    countryCode: string,
    localNumber: string
  ) => {
    dispatch(
      updateTripSummary({
        paymentDetails: {
          ...tripSummaryDetails.paymentDetails,
          phone: phone,
          phone_int_code: countryCode,
          contact_details_phone_number: localNumber,
        },
      })
    );

    // Mark phone field as touched and validate
    markFieldAsTouched("phone_int_code");
  };

  const handleTitleChange = (value: string) => {
    dispatch(
      updateTripSummary({
        paymentDetails: {
          ...tripSummaryDetails.paymentDetails,
          title: value,
        },
      })
    );

    // Mark title field as touched and validate
    markFieldAsTouched("title", value);
  };

  const styles = {
    button: {
      background: "#1E1E76",
      color: "white",
      borderRadius: "8px",
    },
  };

  const handleSuggestionClick = (data: Country, field: string) => {
    dispatch(
      updateTripSummary({
        paymentDetails: {
          ...tripSummaryDetails.paymentDetails,
          country_code: data?.code,
          country_name: data?.name,
        },
      })
    );
    setCountry({ search: data.name, loading: false, countryList: [] });

    // Mark country field as touched and validate
    markFieldAsTouched("country_code");
  };

  const debouncedHandleCountry = useMemo(
    () =>
      debounce(async (query: string) => {
        if (query !== "") {
          try {
            const response = await agentPostMethod(
              "flight/suggest-country",
              { query },
              token ?? ""
            );
            setCountry((prev) => ({
              ...prev,
              search: query,
              loading: false,
              countryList: response?.detail?.data || [],
            }));
          } catch (error) {
            updateGlobalPopup({
              isOpen: true,
              message: "Something Went Wrong .Please try again",
              type: "error",
            });
            console.log("Api error", error);
          }
        }
      }, 500),
    [token, updateGlobalPopup]
  );

  const handleCountry = (query: string) => {
    setCountry((prev) => ({ ...prev, loading: true, search: query }));
    debouncedHandleCountry(query);

    // Mark country field as touched and validate
    markFieldAsTouched("country_code");
  };

  // Enhanced onProceed with full validation
  const handleProceed = () => {
    // Validate all fields before proceeding
    const fieldsToValidate = [
      "title",
      "name",
      "email",
      "city",
      "state",
      "postcode",
      "country_code",
      "phone_int_code",
    ];

    let hasErrors = false;
    const newRealTimeErrors: { [key: string]: string } = {};

    fieldsToValidate.forEach((field) => {
      const error = validateField(field);
      if (error) {
        hasErrors = true;
        newRealTimeErrors[field] = error;
        // Mark all fields as touched so errors show
        setTouchedFields((prev) => new Set([...prev, field]));
      }
    });

    if (hasErrors) {
      setRealTimeErrors(newRealTimeErrors);
      // updateGlobalPopup({
      //   isOpen: true,
      //   message: "Please fill in all required fields correctly",
      //   type: "error",
      // })
      return;
    }

    // If no errors, proceed with payment
    onProceed();
  };

  return (
    <div className="flex flex-col gap-5">
      <div className="relative font-proxima-nova w-full p-px h-auto shadow-sm">
        <div className="flex flex-col gap-4 justify-between w-full 2xl:p-4 xl:p-3 lg:p-2 md:p-3 sm:p-3 xs:p-3 border border-[#EBEBEB] rounded-2xl shadow-md bg-brand-white relative">
          <div className="flex text-2xl text-brand-black font-semibold font-proxima-nova">
            Contact Details
          </div>
          <div className="text-brand-grey">
            Enter your address details for Contact
          </div>
          <div className="flex flex-row xs:flex-col xs:gap-4 gap-5">
            <div className="w-[50%] xs:w-full">
              <div className="relative font-proxima-nova w-full p-px rounded-lg border border-[#EBEBEB] h-auto shadow-sm">
                <Listbox
                  value={tripSummaryDetails?.paymentDetails?.title || ""}
                  onChange={handleTitleChange}
                >
                  <ListboxButton
                    id="title"
                    className="w-full flex justify-between items-center focus:outline-none px-4 xs:px-2 py-2 bg-brand-white text-brand-grey text-left"
                  >
                    <div>
                      {tripSummaryDetails?.paymentDetails?.title ||
                        "Select Title"}
                    </div>
                    <ChevronDown />
                  </ListboxButton>
                  <ListboxOptions className="bg-brand-white border-2 border-[#EBEBEB] p-2 rounded-xl flex flex-col gap-2 cursor-pointer absolute z-10 w-full max-h-60 overflow-auto focus:outline-none">
                    <ListboxOption
                      className="bg-none hover:bg-[#EBEBEB] text-brand-grey"
                      value=""
                    >
                      Select Title
                    </ListboxOption>
                    <ListboxOption
                      className="bg-none hover:bg-[#EBEBEB] text-brand-black"
                      value="Mr"
                    >
                      Mr
                    </ListboxOption>
                    <ListboxOption
                      className="bg-none hover:bg-[#EBEBEB] text-brand-black"
                      value="Mrs"
                    >
                      Mrs
                    </ListboxOption>
                    <ListboxOption
                      className="bg-none hover:bg-[#EBEBEB] text-brand-black"
                      value="Miss"
                    >
                      Miss
                    </ListboxOption>
                    <ListboxOption
                      className="bg-none hover:bg-[#EBEBEB] text-brand-black"
                      value="Dr"
                    >
                      Dr
                    </ListboxOption>
                  </ListboxOptions>
                </Listbox>
              </div>
              {getFieldError("title") && (
                <div className="text-red-500 text-sm mt-1">
                  {getFieldError("title")}
                </div>
              )}
            </div>
            <div className="w-[50%] xs:w-full">
              <div className="relative font-proxima-nova w-full p-px rounded-lg border-2 border-[#EBEBEB] text-brand-grey h-auto shadow-sm">
                <input
                  type="text"
                  id="name"
                  className="w-full px-3 py-2 placeholder:text-brand-grey bg-brand-white focus:outline-none"
                  placeholder="Name"
                  value={tripSummaryDetails?.paymentDetails?.name || ""}
                  onChange={handleChange("name")}
                />
              </div>
              {getFieldError("name") && (
                <div className="text-red-500 text-sm mt-1">
                  {getFieldError("name")}
                </div>
              )}
            </div>
          </div>
          <div className="flex flex-row xs:flex-col xs:gap-4 gap-5">
            <div className="w-[50%] xs:w-full">
              <div className="payment-form relative font-proxima-nova w-full p-px rounded-lg border-2 border-[#EBEBEB] text-brand-grey h-auto shadow-sm">
                <PhoneNumberInput
                  phoneNumberDetails={{
                    phone: tripSummaryDetails?.paymentDetails?.phone,
                    phone_int_code:
                      tripSummaryDetails?.paymentDetails?.phone_int_code,
                    contact_details_phone_number:
                      tripSummaryDetails?.paymentDetails
                        ?.contact_details_phone_number,
                  }}
                  handleCode={handlePhoneNumber}
                />
              </div>
              {getFieldError("phone_int_code") && (
                <div className="text-red-500 text-sm mt-1">
                  {getFieldError("phone_int_code")}
                </div>
              )}
            </div>
            <div className="w-[50%] xs:w-full">
              <div className="relative font-proxima-nova w-full p-px rounded-lg border-2 border-[#EBEBEB] text-brand-grey h-auto shadow-sm">
                <input
                  type="text"
                  id="Email"
                  className="w-full px-3 py-2 placeholder:text-brand-grey bg-brand-white focus:outline-none"
                  placeholder="Email Id"
                  value={tripSummaryDetails?.paymentDetails?.email || ""}
                  onChange={handleChange("email")}
                />
              </div>
              {getFieldError("email") && (
                <div className="text-red-500 text-sm mt-1">
                  {getFieldError("email")}
                </div>
              )}
            </div>
          </div>
          <div className="relative font-proxima-nova w-full p-px rounded-lg border-2 border-[#EBEBEB] text-brand-grey h-auto shadow-sm">
            <input
              type="text"
              id="street address"
              className="w-full px-3 py-2 placeholder:text-brand-grey bg-brand-white focus:outline-none"
              placeholder="Street Address"
              value={tripSummaryDetails.paymentDetails?.street || ""}
              onChange={handleChange("street")}
              // onBlur={() => markFieldAsTouched("street")}
              required
            />
          </div>
          {getFieldError("street") && (
            <div className="text-red-500 text-sm mt-1">
              {getFieldError("street")}
            </div>
          )}
          <div className="flex flex-row xs:flex-col xs:gap-4 gap-5">
            <div className="w-[50%] xs:w-full">
              <div className="relative font-proxima-nova w-full p-px rounded-lg border-2 border-[#EBEBEB] text-brand-grey h-auto shadow-sm">
                <input
                  type="text"
                  id="City"
                  className="w-full px-3 py-2 placeholder:text-brand-grey bg-brand-white focus:outline-none"
                  placeholder="City"
                  value={tripSummaryDetails.paymentDetails?.city || ""}
                  onChange={handleChange("city")}
                />
              </div>
              {getFieldError("city") && (
                <div className="text-red-500 text-sm">
                  {getFieldError("city")}
                </div>
              )}
            </div>
            <div className="w-[50%] xs:w-full">
              <div className="relative font-proxima-nova w-full p-px rounded-lg border-2 border-[#EBEBEB] text-brand-grey h-auto shadow-sm">
                <input
                  type="text"
                  id="State"
                  className="w-full px-3 py-2 placeholder:text-grey bg-brand-white focus:outline-none"
                  placeholder="State / Province"
                  value={tripSummaryDetails.paymentDetails?.state || ""}
                  onChange={handleChange("state")}
                />
              </div>
              {getFieldError("state") && (
                <div className="text-red-500 text-sm">
                  {getFieldError("state")}
                </div>
              )}
            </div>
          </div>
          <div className="flex flex-row xs:flex-col xs:gap-4 gap-5">
            <div className="w-[50%] xs:w-full">
              <div className="relative font-proxima-nova w-full p-px rounded-lg border-2 border-[#EBEBEB] text-brand-grey h-auto shadow-sm">
                <input
                  type="text"
                  id="Zip Code"
                  className="w-full px-3 py-2 placeholder:text-brand-grey bg-brand-white focus:outline-none"
                  placeholder="Zip Code"
                  value={tripSummaryDetails.paymentDetails?.postcode || ""}
                  onChange={handleChange("postcode")}
                />
              </div>
              {getFieldError("postcode") && (
                <div className="text-red-500 text-sm">
                  {getFieldError("postcode")}
                </div>
              )}
            </div>
            <div className="w-[50%] xs:w-full">
              <div className="relative font-proxima-nova w-full p-px rounded-lg border-2 border-[#EBEBEB] text-brand-grey h-auto shadow-sm">
                <input
                  type="text"
                  id="Country"
                  className="w-full px-3 py-2 placeholder:text-brand-grey bg-brand-white focus:outline-none"
                  placeholder="Country"
                  value={country.search}
                  onChange={(e) => handleCountry(e.target.value)}
                />
                {country.search !== "" &&
                  country.countryList &&
                  country.countryList.length > 0 && (
                    <div className="absolute z-10 mt-2 w-full bg-white text-brand-black rounded-md shadow-lg max-h-60 overflow-auto">
                      {country.loading ? (
                        <div className="px-4 py-2 text-brand-black">
                          Loading suggestions...
                        </div>
                      ) : (
                        country.countryList.map((suggestion, index) => (
                          <div
                            key={`destination-${suggestion?.name}-${index}`}
                            className="px-4 py-2 hover:bg-brand-grey cursor-pointer text-brand-black"
                            onClick={() =>
                              handleSuggestionClick(suggestion, "country_code")
                            }
                          >
                            {suggestion.name}
                          </div>
                        ))
                      )}
                    </div>
                  )}
              </div>
              {getFieldError("country_code") && (
                <div className="text-red-500 text-sm mt-1">
                  {getFieldError("country_code")}
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
      <div className="flex flex-col gap-2 font-proxima-nova items-center w-full justify-center">
        <div className="flex gap-2 ">
          <div>
            <input
              type="checkbox"
              className="accent-[#4B4BC3]"
              onChange={(e) => setEnablePay(e.target.checked)}
            />
          </div>
          <div className="w-full text-sm text-[#080236]">
            By selecting 'Pay now', I agree to proceed with this purchase and
            accept the{" "}
            <a
              href="/terms-of-service"
              target="_blank"
              rel="noopener noreferrer"
              className="underline text-[#080236] hover:text-[#1E1E76]"
            >
              terms and conditions
            </a>{" "}
            and{" "}
            <a
              href="/privacy-policy"
              target="_blank"
              rel="noopener noreferrer"
              className="underline text-[#080236] hover:text-[#1E1E76]"
            >
              privacy policy
            </a>
            . This sale is not protected under the ATOL scheme.
          </div>
        </div>
        <button
          className={`px-4 py-2 text-[#F2F3FA] w-1/3 xs:w-2/3 ${
            !enablePay ? "opacity-50 cursor-not-allowed" : "opacity-100"
          }`}
          style={styles.button}
          onClick={handleProceed}
          disabled={!enablePay}
        >
          Pay now
        </button>
      </div>
    </div>
  );
};

export default PaymentForm;
